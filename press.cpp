#include <iostream>
#include <windows.h>
#include <random>

int main() {
    std::cout << "X tusuna basin, sonsuz dongu baslasin.\n";
    std::cout << "Tekrar X'e basin, dursun.\n";
    std::cout << "Cikmak icin Ctrl+C kullanin.\n\n";

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(2000,2100); // 1.5-3 saniye arası (milisaniye)

    bool isRunning = false;

    while (true) {
        if (GetAsyncKeyState('X') & 0x8000) {
            while (GetAsyncKeyState('X') & 0x8000) {
                Sleep(50);
            }

            if (!isRunning) {
                std::cout << "X basildi! Sonsuz dongu basliyor...\n";
                std::cout << "Durdurmak icin tekrar X'e basin.\n\n";
                isRunning = true;
            } else {
                std::cout << "X basildi! Dongu durduruluyor...\n\n";
                isRunning = false;
            }
        }

        if (isRunning) {
            // 20 defa W
            for (int i = 0; i < 20 && isRunning; i++) {
                if (GetAsyncKeyState('X') & 0x8000) {
                    while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                    std::cout << "\nX basildi! Dongu durduruluyor...\n\n";
                    isRunning = false;
                    break;
                }

                keybd_event('W', 0, 0, 0);
                Sleep(500);
                keybd_event('W', 0, KEYEVENTF_KEYUP, 0);

                std::cout << "W " << (i+1) << "/20\n";

                if (i < 19 && isRunning) {
                    int wait_time = dis(gen);
                    for (int j = 0; j < wait_time / 100 && isRunning; j++) {
                        if (GetAsyncKeyState('X') & 0x8000) {
                            while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                            std::cout << "\nX basildi! Dongu durduruluyor...\n\n";
                            isRunning = false;
                            break;
                        }
                        Sleep(100);
                    }
                }
            }

            if (isRunning) {
                std::cout << "\nS tusuna tek seferde 10 saniye basiliyor...\n";

                // S tuşuna tek sefer bas 10 saniye boyunca
                keybd_event('S', 0, 0, 0);
                Sleep(10000);
                keybd_event('S', 0, KEYEVENTF_KEYUP, 0);

                std::cout << "S islemi tamamlandi.\n";

                // Ardından 10 saniye bekle
                std::cout << "10 saniye bekleniyor...\n";
                for (int i = 0; i < 50 && isRunning; i++) {
                    if (GetAsyncKeyState('X') & 0x8000) {
                        while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                        std::cout << "\nX basildi! Dongu durduruluyor...\n\n";
                        isRunning = false;
                        break;
                    }
                    Sleep(100);
                }

                if (isRunning) {
                    std::cout << "\nDongu tamamlandi, tekrar basliyor...\n\n";
                }
            }
        }

        Sleep(100);
    }

    return 0;
}
